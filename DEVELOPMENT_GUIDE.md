# Development Guide

## Vite Proxy Configuration

This project uses Vite for frontend development with automatic API proxying to the backend server.

### How It Works

1. **Frontend Development Server**: Vite runs on port 8080
2. **Backend API Server**: Express + DBOS runs on port 3000
3. **Automatic Proxy**: All `/api/*` requests are automatically forwarded from frontend to backend

### Configuration Details

The proxy is configured in `vite.config.ts`:

```typescript
server: {
  host: "::",
  port: 8080,
  proxy: {
    '/api': {
      target: 'http://localhost:3000',
      changeOrigin: true,
      secure: false,
    },
  },
}
```

### Development Workflow

1. **Start both servers**:
   ```bash
   npm run dev:full
   ```

2. **Or start separately**:
   ```bash
   # Terminal 1
   npm run dev:backend
   
   # Terminal 2  
   npm run dev
   ```

3. **Access the application**:
   - Frontend: http://localhost:8080
   - Backend API: http://localhost:3000
   - Health check: http://localhost:3000/health

### API Routes

All API routes are defined in `src/routes/` and mounted in `src/routes/index.ts`:

- `/api/compliance/*` → `src/routes/compliance.ts`
- `/api/kyc/*` → `src/routes/kyc.ts`
- `/api/reports/*` → `src/routes/reports.ts`
- `/api/dashboard/*` → `src/routes/dashboard.ts`
- `/api/documents/*` → `src/routes/documents.ts`
- `/api/regulatory/*` → `src/routes/regulatory.ts`
- `/api/workflow/*` → `src/routes/workflows.ts`

### Frontend API Client

The frontend uses `src/services/complianceApi.ts` to make API calls:

```typescript
const API_BASE_URL = '/'; // Uses proxy in development

// Example API call
const response = await fetch(`${API_BASE_URL}/api/compliance/document`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(documentData),
});
```

### Troubleshooting

**Problem**: API calls return 404 or connection errors
**Solution**: 
1. Ensure backend server is running on port 3000
2. Check that proxy configuration is correct in `vite.config.ts`
3. Verify API routes are properly defined in `src/routes/`

**Problem**: CORS errors
**Solution**: The proxy should handle CORS automatically. If issues persist, check the CORS middleware in `src/middleware/cors.ts`

**Problem**: Hot reloading not working
**Solution**: 
1. Restart both servers
2. Clear browser cache
3. Check console for any JavaScript errors

### Production Build

For production, the frontend is built and served as static files by the Express server:

```bash
npm run build
npm run start
```

This serves the built frontend from the `dist/` directory alongside the API routes.
