import { Router, Request, Response } from 'express';
import { DBOS } from "@dbos-inc/dbos-sdk";
import { ComplianceSystem } from '../workflows';
import { ComplianceDatabase } from '../database';
import { reportingQueue } from '../config/queues';

const router = Router();

// Generate compliance report
router.post('/generate', async (req: Request, res: Response): Promise<void> => {
  try {
    const { reportType, dateRange, standards } = req.body;

    // Validate report type (now accepting the actual report type IDs from frontend)
    const validReportTypes = [
      'compliance-summary',
      'kyc-status',
      'violations-analysis',
      'regulatory-impact',
      'audit-trail'
    ];

    if (!reportType || !validReportTypes.includes(reportType)) {
      res.status(400).json({
        error: 'Invalid report type',
        validTypes: validReportTypes
      });
      return;
    }

    // Determine time period from dateRange or default to monthly
    let timePeriod: 'monthly' | 'quarterly' | 'annual' = 'monthly';
    if (dateRange) {
      const start = new Date(dateRange.startDate);
      const end = new Date(dateRange.endDate);
      const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff > 300) {
        timePeriod = 'annual';
      } else if (daysDiff > 80) {
        timePeriod = 'quarterly';
      } else {
        timePeriod = 'monthly';
      }
    }

    // Start report generation workflow with enhanced parameters
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: reportingQueue.name }
    ).generateComplianceReport(timePeriod, reportType, dateRange, standards);

    res.json({
      workflowId: handle.workflowID,
      status: 'report_generation_started',
      message: `${reportType} compliance report generation initiated`,
      reportType,
      timePeriod,
      dateRange
    });
  } catch (error) {
    DBOS.logger.error(`Error generating report: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get recent reports
router.get('/recent', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📄 Recent reports requested');

    // Get real recent reports from database
    const recentReports = await ComplianceDatabase.getRecentReports();

    res.json(recentReports);
  } catch (error) {
    console.error('Error fetching recent reports:', error);
    res.status(500).json({ error: 'Failed to fetch recent reports' });
  }
});

// Get report stats
router.get('/stats', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📈 Report stats requested');

    // Get real report statistics from database
    const stats = await ComplianceDatabase.getReportStats();

    res.json(stats);
  } catch (error) {
    console.error('Error fetching report stats:', error);
    res.status(500).json({ error: 'Failed to fetch report stats' });
  }
});

export default router;
